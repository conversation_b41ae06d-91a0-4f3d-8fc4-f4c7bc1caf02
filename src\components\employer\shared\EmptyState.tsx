import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { <PERSON><PERSON>, Card, Stack, Text, Title } from "@mantine/core";
import {type ReactNode } from "react";
import { Link } from "react-router";

interface EmptyStateProps {
  icon?: ReactNode;
  title: string;
  description: string;
  action?: {
    label: string;
    href?: string;
    onClick?: () => void;
    icon?: ReactNode;
    variant?: "filled" | "outline" | "light";
    color?: string;
  };
  variant?: "card" | "page";
  size?: "sm" | "md" | "lg";
}

export default function EmptyState({
  icon,
  title,
  description,
  action,
  variant = "card",
  size = "md",
}: EmptyStateProps) {
  const sizeClasses = {
    sm: {
      title: "h4",
      text: "sm",
      icon: "text-4xl",
      padding: "md",
    },
    md: {
      title: "h3",
      text: "md",
      icon: "text-6xl",
      padding: "xl",
    },
    lg: {
      title: "h2",
      text: "lg",
      icon: "text-8xl",
      padding: "2xl",
    },
  };

  const content = (
    <Stack align="center" gap="md" className="text-center">
      {icon && (
        <div
          className={`${sizeClasses[size].icon} ${useThemeClasses(
            "text-gray-400",
            "text-gray-600",
          )}`}
        >
          {icon}
        </div>
      )}

      <div>
        <Title
          order={sizeClasses[size].title === "h2" ? 2 : sizeClasses[size].title === "h3" ? 3 : 4}
          className={useThemeClasses(
            "mb-2 text-gray-700",
            "mb-2 text-gray-300",
          )}
        >
          {title}
        </Title>
        <Text
          size={sizeClasses[size].text as any}
          c="dimmed"
          className="max-w-md"
        >
          {description}
        </Text>
      </div>

      {action && (
        <div className="mt-4">
          {action.href ? (
            <Link to={action.href}>
              <Button
                variant={action.variant || "filled"}
                color={action.color || "blue"}
                leftSection={action.icon}
                size={size}
              >
                {action.label}
              </Button>
            </Link>
          ) : (
            <Button
              variant={action.variant || "filled"}
              color={action.color || "blue"}
              leftSection={action.icon}
              onClick={action.onClick}
              size={size}
            >
              {action.label}
            </Button>
          )}
        </div>
      )}
    </Stack>
  );

  if (variant === "page") {
    return (
      <div className="flex min-h-96 items-center justify-center">
        {content}
      </div>
    );
  }

  return (
    <Card
      withBorder
      radius="md"
      className={useThemeClasses(
        "bg-white shadow-sm",
        "bg-dark-6 shadow-dark-lg",
      )}
      p={sizeClasses[size].padding as any}
    >
      {content}
    </Card>
  );
}
