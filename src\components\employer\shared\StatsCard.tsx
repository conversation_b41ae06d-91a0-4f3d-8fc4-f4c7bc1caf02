import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { Card, Group, Text } from "@mantine/core";
import {type ReactNode } from "react";

interface StatsCardProps {
  icon: ReactNode;
  value: string | number;
  label: string;
  color?: string;
  size?: "sm" | "md" | "lg";
  variant?: "default" | "compact";
}

export default function StatsCard({
  icon,
  value,
  label,
  color = "blue",
  size = "md",
  variant = "default",
}: StatsCardProps) {
  const sizeClasses = {
    sm: "text-lg",
    md: "text-xl",
    lg: "text-2xl",
  };

  const iconColorClass = `text-${color}-500`;

  if (variant === "compact") {
    return (
      <Group gap="xs">
        <div className={iconColorClass}>{icon}</div>
        <div>
          <Text fw={700} size={size === "sm" ? "md" : "xl"}>
            {value}
          </Text>
          <Text size="xs" c="dimmed">
            {label}
          </Text>
        </div>
      </Group>
    );
  }

  return (
    <Card
      withBorder
      radius="md"
      className={useThemeClasses(
        "bg-white shadow-sm transition-all duration-200 hover:shadow-md",
        "bg-dark-6 shadow-dark-lg transition-all duration-200 hover:shadow-dark-xl",
      )}
      p="md"
    >
      <Group gap="md" align="center">
        <div
          className={useThemeClasses(
            `flex h-12 w-12 items-center justify-center rounded-full bg-${color}-100 ${iconColorClass}`,
            `flex h-12 w-12 items-center justify-center rounded-full bg-${color}-900/30 text-${color}-300`,
          )}
        >
          {icon}
        </div>
        <div>
          <Text fw={700} className={sizeClasses[size]}>
            {value}
          </Text>
          <Text size="sm" c="dimmed">
            {label}
          </Text>
        </div>
      </Group>
    </Card>
  );
}
