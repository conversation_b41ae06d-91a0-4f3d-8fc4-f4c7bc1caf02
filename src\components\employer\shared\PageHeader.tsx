import { useThemeClasses } from "@/design-system/utils/theme-utils";
import { <PERSON><PERSON>, Card, Grid, Group, Text, Title } from "@mantine/core";
import { type ReactNode } from "react";
import { Link } from "react-router";

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  children?: ReactNode;
  actions?: {
    label: string;
    href?: string;
    onClick?: () => void;
    icon?: ReactNode;
    variant?: "filled" | "outline" | "light";
    color?: string;
  }[];
  stats?: {
    icon: ReactNode;
    value: string | number;
    label: string;
    color?: string;
  }[];
  gradient?: boolean;
}

export default function PageHeader({
  title,
  subtitle,
  children,
  actions,
  stats,
  gradient = true,
}: PageHeaderProps) {
  return (
    <Card withBorder radius="md" className="mb-8 overflow-hidden">
      <div className="relative">
        {gradient && (
          <div
            className={useThemeClasses(
              "absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50",
              "absolute inset-0 bg-gradient-to-r from-dark-6 to-dark-5",
            )}
          />
        )}

        <div className="relative p-6 md:p-8">
          <Grid>
            <Grid.Col span={{ base: 12, md: stats ? 8 : 12 }}>
              <div className="mb-4">
                <Title
                  order={1}
                  className={useThemeClasses(
                    "mb-2 text-2xl font-bold text-gray-800",
                    "mb-2 text-2xl font-bold text-gray-100",
                  )}
                >
                  {title}
                </Title>
                {subtitle && (
                  <Text
                    className={useThemeClasses(
                      "text-gray-600",
                      "text-gray-400",
                    )}
                  >
                    {subtitle}
                  </Text>
                )}
              </div>

              {/* Actions */}
              {actions && actions.length > 0 && (
                <Group gap="md" className="mb-4">
                  {actions.map((action, index) => {
                    const button = (
                      <Button
                        key={index}
                        variant={action.variant || "filled"}
                        color={action.color || "blue"}
                        leftSection={action.icon}
                        onClick={action.onClick}
                      >
                        {action.label}
                      </Button>
                    );

                    return action.href ? (
                      <Link key={index} to={action.href}>
                        {button}
                      </Link>
                    ) : (
                      button
                    );
                  })}
                </Group>
              )}

              {/* Stats in compact mode */}
              {stats && (
                <Grid className="mt-6">
                  {stats.map((stat, index) => (
                    <Grid.Col key={index} span={{ base: 6, md: 3 }}>
                      <Group gap="xs">
                        <div className={`text-${stat.color || "blue"}-500`}>
                          {stat.icon}
                        </div>
                        <div>
                          <Text fw={700} size="xl">
                            {stat.value}
                          </Text>
                          <Text size="xs" c="dimmed">
                            {stat.label}
                          </Text>
                        </div>
                      </Group>
                    </Grid.Col>
                  ))}
                </Grid>
              )}

              {children}
            </Grid.Col>

            {/* Right side content for additional info */}
            {stats && (
              <Grid.Col span={{ base: 12, md: 4 }}>
                <Card
                  withBorder
                  radius="md"
                  className={useThemeClasses(
                    "bg-white shadow-sm",
                    "bg-dark-6 shadow-dark-lg",
                  )}
                >
                  {/* This can be customized per page */}
                  <Text fw={700} ta="center" className="mb-2">
                    Quick Stats
                  </Text>
                  <Text size="sm" c="dimmed" ta="center">
                    Overview of your activity
                  </Text>
                </Card>
              </Grid.Col>
            )}
          </Grid>
        </div>
      </div>
    </Card>
  );
}
